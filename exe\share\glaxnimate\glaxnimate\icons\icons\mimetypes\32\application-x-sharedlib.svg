<svg width="32" xmlns="http://www.w3.org/2000/svg" height="32" xmlns:xlink="http://www.w3.org/1999/xlink">
 <defs>
  <linearGradient id="a" y1="392.36" y2="365.36" gradientUnits="userSpaceOnUse" x2="0" gradientTransform="translate(309.57 152.44)">
   <stop stop-color="#ffffff" stop-opacity="0"/>
   <stop offset="1" stop-color="#ffffff" stop-opacity=".2"/>
  </linearGradient>
  <linearGradient id="b" y1="17" x1="47" y2="24" gradientUnits="userSpaceOnUse" x2="54" gradientTransform="translate(357.57 507.8)">
   <stop stop-color="#060606"/>
   <stop offset="1" stop-opacity="0"/>
  </linearGradient>
  <path id="c" d="m389.57 545.8v-28h15l7 7v21h-14z"/>
 </defs>
 <g transform="translate(-384.57-515.8)">
  <g color-rendering="auto" color-interpolation-filters="linearRGB" shape-rendering="auto" image-rendering="auto" text-rendering="auto" color-interpolation="sRGB" color="#000000">
   <use fill="#555555" xlink:href="#c"/>
   <g transform="scale(1-1)">
    <rect opacity=".4" x="389.57" y="-518.8" width="15" fill="#ffffff" height="1"/>
    <rect opacity=".35" x="389.57" y="-545.8" width="22" height="1"/>
   </g>
   <path opacity=".5" fill="#ffffff" fill-rule="evenodd" d="m411.57 524.8l-7-7v7z"/>
  </g>
  <path opacity=".4" fill="url(#b)" fill-rule="evenodd" d="m404.57 524.8l7 7v-7z"/>
  <path fill="#343434" d="m403.56 531.91c.019-.152.033-.306.033-.463 0-.157-.014-.311-.033-.463l1-.785c.09-.071.116-.199.057-.304l-.949-1.644c-.059-.102-.183-.145-.289-.102l-1.182.477c-.244-.187-.513-.346-.802-.467l-.178-1.258c-.021-.112-.119-.199-.237-.199h-1.898c-.119 0-.216.088-.235.199l-.178 1.258c-.289.121-.558.278-.802.467l-1.182-.477c-.107-.04-.23 0-.289.102l-.949 1.644c-.059.102-.033.23.057.304l1 .785c-.019.152-.033.306-.033.463 0 .157.014.311.033.463l-1 .785c-.09.071-.116.199-.057.304l.949 1.644c.059.102.183.145.289.102l1.182-.477c.244.187.513.346.802.467l.178 1.258c.019.112.116.199.235.199h1.898c.119 0 .216-.088.235-.199l.178-1.258c.289-.121.558-.278.802-.467l1.182.477c.107.04.23 0 .289-.102l.949-1.644c.059-.102.033-.23-.057-.304zm-3.526 1.198c-.918 0-1.661-.743-1.661-1.661 0-.918.743-1.661 1.661-1.661.918 0 1.661.743 1.661 1.661 0 .918-.743 1.661-1.661 1.661"/>
  <path fill="#e0e0e0" d="m402.56 530.91c.019-.152.033-.306.033-.463 0-.157-.014-.311-.033-.463l1-.785c.09-.071.116-.199.057-.304l-.949-1.644c-.059-.102-.183-.145-.289-.102l-1.182.477c-.244-.187-.513-.346-.802-.467l-.178-1.258c-.021-.112-.119-.199-.237-.199h-1.898c-.119 0-.216.088-.235.199l-.178 1.258c-.289.121-.558.278-.802.467l-1.182-.477c-.107-.04-.23 0-.289.102l-.949 1.644c-.059.102-.033.23.057.304l1 .785c-.019.152-.033.306-.033.463 0 .157.014.311.033.463l-1 .785c-.09.071-.116.199-.057.304l.949 1.644c.059.102.183.145.289.102l1.182-.477c.244.187.513.346.802.467l.178 1.258c.019.112.116.199.235.199h1.898c.119 0 .216-.088.235-.199l.178-1.258c.289-.121.558-.278.802-.467l1.182.477c.107.04.23 0 .289-.102l.949-1.644c.059-.102.033-.23-.057-.304zm-3.526 1.198c-.918 0-1.661-.743-1.661-1.661 0-.918.743-1.661 1.661-1.661.918 0 1.661.743 1.661 1.661 0 .918-.743 1.661-1.661 1.661"/>
  <path fill="#343434" d="m406.27 540.3c.065-.091.129-.185.184-.286.055-.101.1-.205.141-.309l.921-.153c.083-.014.145-.087.143-.175l-.033-1.39c-.003-.086-.067-.157-.15-.167l-.927-.108c-.091-.206-.208-.402-.351-.582l.327-.871c.025-.079-.006-.17-.083-.211l-1.22-.666c-.076-.042-.17-.019-.221.046l-.555.746c-.228-.024-.456-.017-.679.019l-.592-.721c-.055-.063-.148-.081-.222-.036l-1.187.724c-.074.045-.102.136-.07.215l.368.856c-.065.091-.129.185-.184.286-.055.101-.1.205-.141.309l-.919.154c-.083.014-.145.087-.143.175l.033 1.39c.003.086.067.157.15.167l.927.108c.091.206.208.402.351.582l-.327.871c-.027.078.005.169.081.21l1.22.666c.076.042.17.019.221-.046l.555-.746c.228.024.456.017.679-.019l.592.721c.055.063.148.081.222.036l1.187-.724c.074-.045.102-.136.07-.215zm-2.686-.467c-.59-.322-.807-1.06-.485-1.65.322-.59 1.06-.807 1.65-.485.59.322.807 1.06.485 1.65-.322.59-1.06.807-1.65.485"/>
  <path fill="#e0e0e0" d="m405.27 539.3c.065-.091.129-.185.184-.286.055-.101.1-.205.141-.309l.921-.153c.083-.014.145-.087.143-.175l-.033-1.39c-.003-.086-.067-.157-.15-.167l-.927-.108c-.091-.206-.208-.402-.351-.582l.327-.871c.025-.079-.006-.17-.083-.211l-1.22-.666c-.076-.042-.17-.019-.221.046l-.555.746c-.228-.024-.456-.017-.679.019l-.592-.721c-.055-.063-.148-.081-.222-.036l-1.187.724c-.074.045-.102.136-.07.215l.368.856c-.065.091-.129.185-.184.286-.055.101-.1.205-.141.309l-.919.154c-.083.014-.145.087-.143.175l.033 1.39c.003.086.067.157.15.167l.927.108c.091.206.208.402.351.582l-.327.871c-.027.078.005.169.081.21l1.22.666c.076.042.17.019.221-.046l.555-.746c.228.024.456.017.679-.019l.592.721c.055.063.148.081.222.036l1.187-.724c.074-.045.102-.136.07-.215zm-2.686-.467c-.59-.322-.807-1.06-.485-1.65.322-.59 1.06-.807 1.65-.485.59.322.807 1.06.485 1.65-.322.59-1.06.807-1.65.485"/>
  <use fill="url(#a)" xlink:href="#c"/>
 </g>
</svg>
