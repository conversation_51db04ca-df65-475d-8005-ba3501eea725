<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Created with Inkscape (http://www.inkscape.org/) -->

<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   width="22"
   version="1.1"
   height="22"
   id="svg2"
   inkscape:version="0.91 r13725"
   sodipodi:docname="application-vnd.ms-infopath.svg">
  <sodipodi:namedview
     pagecolor="#ffffff"
     bordercolor="#666666"
     borderopacity="1"
     objecttolerance="10"
     gridtolerance="10"
     guidetolerance="10"
     inkscape:pageopacity="0"
     inkscape:pageshadow="2"
     inkscape:window-width="1360"
     inkscape:window-height="708"
     id="namedview14"
     showgrid="true"
     inkscape:zoom="11.313708"
     inkscape:cx="-1.9132954"
     inkscape:cy="15.19605"
     inkscape:window-x="0"
     inkscape:window-y="0"
     inkscape:window-maximized="1"
     inkscape:current-layer="layer1"
     inkscape:object-nodes="true">
    <inkscape:grid
       type="xygrid"
       id="grid4151" />
  </sodipodi:namedview>
  <defs
     id="defs3871" />
  <metadata
     id="metadata3874">
    <rdf:RDF>
      <cc:Work
         rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
      </cc:Work>
    </rdf:RDF>
  </metadata>
  <g
     inkscape:label="Capa 1"
     inkscape:groupmode="layer"
     id="layer1"
     transform="matrix(1 0 0 1 -326 -534.3622)">
    <path
       style="color:#000000;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:medium;line-height:normal;font-family:sans-serif;text-indent:0;text-align:start;text-decoration:none;text-decoration-line:none;text-decoration-style:solid;text-decoration-color:#000000;letter-spacing:normal;word-spacing:normal;text-transform:none;direction:ltr;block-progression:tb;writing-mode:lr-tb;baseline-shift:baseline;text-anchor:start;white-space:normal;clip-rule:nonzero;display:inline;overflow:visible;visibility:visible;opacity:1;isolation:auto;mix-blend-mode:normal;color-interpolation:sRGB;color-interpolation-filters:linearRGB;solid-color:#000000;solid-opacity:1;fill:#8542c2;fill-opacity:1;fill-rule:evenodd;stroke:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;color-rendering:auto;image-rendering:auto;shape-rendering:auto;text-rendering:auto;enable-background:accumulate"
       d="M 14 2 L 10 6 L 12 6 L 12 7 L 7 7 L 6 7 L 6 16 L 4 16 L 8 20 L 12 16 L 10 16 L 10 15 L 16 15 L 16 14 L 16 6 L 18 6 L 14 2 z M 14 3.4140625 L 15.585938 5 L 15 5 L 15 6 L 15 14 L 10 14 L 10 13 L 13 13 L 13 12 L 13 6 L 13 5 L 12.414062 5 L 14 3.4140625 z M 7 8 L 12 8 L 12 9 L 10 9 L 9 9 L 9 10 L 9 12 L 9 13 L 9 14 L 9 15 L 9 16 L 9 17 L 9.5859375 17 L 8 18.585938 L 6.4140625 17 L 7 17 L 7 16 L 7 8 z M 10 10 L 12 10 L 12 12 L 10 12 L 10 10 z "
       transform="translate(326,534.3622)"
       id="path4153" />
  </g>
</svg>
