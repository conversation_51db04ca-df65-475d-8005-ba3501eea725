<!-- Created with Inkscape (http://www.inkscape.org/) --><svg xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:svg="http://www.w3.org/2000/svg" xmlns="http://www.w3.org/2000/svg" xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd" xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape" width="22" version="1.1" height="22" id="svg2" inkscape:version="0.91 r13725" sodipodi:docname="application-vnd.ms-excel.addin.macroenabled.12.svg">
  <sodipodi:namedview pagecolor="#ffffff" bordercolor="#666666" borderopacity="1" objecttolerance="10" gridtolerance="10" guidetolerance="10" inkscape:pageopacity="0" inkscape:pageshadow="2" inkscape:window-width="1360" inkscape:window-height="708" id="namedview11" showgrid="true" inkscape:zoom="17.437253" inkscape:cx="2.1035423" inkscape:cy="7.8372487" inkscape:window-x="0" inkscape:window-y="0" inkscape:window-maximized="1" inkscape:current-layer="excel" inkscape:object-nodes="true">
    <inkscape:grid type="xygrid" id="grid4151"/>
  </sodipodi:namedview>
  <defs id="defs3871"/>
  <metadata id="metadata3874">
    <rdf:RDF>
      <cc:Work rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
      </cc:Work>
    </rdf:RDF>
  </metadata>
  <g inkscape:label="Capa 1" inkscape:groupmode="layer" id="layer1" transform="matrix(1 0 0 1 -326 -534.3622)">
    <g transform="matrix(0.18182176,0,0,0.18182176,306.98141,537.71654)" id="excel">
      <path style="color:#000000;color-interpolation:sRGB;color-interpolation-filters:linearRGB;fill:#25bb70;color-rendering:auto;image-rendering:auto;shape-rendering:auto;text-rendering:auto;fill-opacity:1" d="M 4.3339844 3 C 3.5953171 3 3 3.5953177 3 4.3339844 L 3 17.666016 C 3 18.404682 3.5953171 19 4.3339844 19 L 17.666016 19 C 18.404683 19 19 18.404682 19 17.666016 L 19 4.3339844 C 19 3.5953177 18.404683 3 17.666016 3 L 4.3339844 3 z M 4 4 L 18 4 L 18 18 L 4 18 L 4 4 z M 5 5 L 5 6 L 8 6 L 8 5 L 5 5 z M 9 5 L 9 6 L 13 6 L 13 5 L 9 5 z M 14 5 L 14 6 L 17 6 L 17 5 L 14 5 z M 5 7 L 5 8 L 8 8 L 8 7 L 5 7 z M 9 7 L 9 8 L 13 8 L 13 7 L 9 7 z M 14 7 L 14 8 L 17 8 L 17 7 L 14 7 z M 5 9 L 5 10 L 8 10 L 8 9 L 5 9 z M 9 9 L 9 10 L 13 10 L 13 9 L 9 9 z M 14 9 L 14 10 L 17 10 L 17 9 L 14 9 z M 5 11 L 5 12 L 8 12 L 8 11 L 5 11 z M 9 11 L 9 12 L 13 12 L 13 11 L 9 11 z M 14 11 L 14 12 L 17 12 L 17 11 L 14 11 z M 5 13 L 5 14 L 8 14 L 8 13 L 5 13 z M 9 13 L 9 14 L 13 14 L 13 13 L 9 13 z M 14 13 L 14 14 L 17 14 L 17 13 L 14 13 z M 5 15 L 5 16 L 8 16 L 8 15 L 5 15 z M 9 15 L 9 16 L 13 16 L 13 15 L 9 15 z M 14 15 L 14 16 L 17 16 L 17 15 L 14 15 z " transform="matrix(5.4998918,0,0,5.4998918,104.60019,-18.448507)" id="path4"/>
    </g>
  </g>
</svg>