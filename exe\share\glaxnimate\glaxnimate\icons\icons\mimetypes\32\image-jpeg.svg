<svg width="32" xmlns="http://www.w3.org/2000/svg" height="32" xmlns:xlink="http://www.w3.org/1999/xlink">
 <defs>
  <linearGradient id="a" y1="392.36" y2="320.48" gradientUnits="userSpaceOnUse" x2="0" gradientTransform="matrix(.5 0 0 .42294 378.57 378.93)">
   <stop stop-color="#ffffff" stop-opacity="0"/>
   <stop offset="1" stop-color="#ffffff" stop-opacity=".2"/>
  </linearGradient>
  <linearGradient id="b" y1="17" x1="47" y2="24" gradientUnits="userSpaceOnUse" x2="54" gradientTransform="translate(360.57 510.8)">
   <stop stop-color="#060606"/>
   <stop offset="1" stop-opacity="0"/>
  </linearGradient>
  <path color-rendering="auto" color-interpolation-filters="linearRGB" shape-rendering="auto" image-rendering="auto" text-rendering="auto" id="c" color-interpolation="sRGB" color="#000000" d="m386.57 545.8v-25h21l7 7v18h-7z"/>
 </defs>
 <g transform="translate(-384.57-515.8)">
  <use fill="#4bc94b" xlink:href="#c"/>
  <g fill-rule="evenodd">
   <path opacity=".5" fill="#ffffff" d="m414.57 527.8l-7-7v7z"/>
   <path opacity=".2" fill="url(#b)" d="m407.57 527.8l7 7v-7z"/>
  </g>
  <g color-rendering="auto" color-interpolation-filters="linearRGB" shape-rendering="auto" image-rendering="auto" text-rendering="auto" color-interpolation="sRGB" color="#000000">
   <rect opacity=".25" x="386.57" y="544.8" width="28" height="1"/>
   <g fill="#ffffff">
    <rect opacity=".5" x="386.57" y="520.8" width="21" height="1"/>
    <path opacity=".75" stroke-width="2" d="M11.5 13A1.5 1.5 0 0 0 10 14.5 1.5 1.5 0 0 0 11.5 16 1.5 1.5 0 0 0 13 14.5 1.5 1.5 0 0 0 11.5 13M11.5 14A.5 .5 0 0 1 12 14.5 .5 .5 0 0 1 11.5 15 .5 .5 0 0 1 11 14.5 .5 .5 0 0 1 11.5 14M21 15L15.4 20.58 12.801 18 6.801 24H8.207L11.326 20.918 12.734 19.514 14.299 20.932 15.4 22 21 16.602 24.594 20H26z" transform="translate(384.57 515.8)"/>
   </g>
  </g>
  <use fill="url(#a)" xlink:href="#c"/>
 </g>
</svg>
