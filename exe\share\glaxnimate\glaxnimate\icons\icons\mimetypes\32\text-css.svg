<svg width="32" xmlns="http://www.w3.org/2000/svg" height="32" xmlns:xlink="http://www.w3.org/1999/xlink">
 <defs>
  <linearGradient id="a" y1="17" x1="47" y2="24" gradientUnits="userSpaceOnUse" x2="54" gradientTransform="translate(357.57 507.8)">
   <stop stop-color="#060606"/>
   <stop offset="1" stop-opacity="0"/>
  </linearGradient>
  <linearGradient id="b" y1="392.36" y2="365.36" gradientUnits="userSpaceOnUse" x2="0" gradientTransform="translate(309.57 152.44)">
   <stop stop-color="#ffffff" stop-opacity="0"/>
   <stop offset="1" stop-color="#ffffff" stop-opacity=".2"/>
  </linearGradient>
  <path id="c" d="m389.57 545.8v-28h15l7 7v21h-14z"/>
 </defs>
 <g transform="translate(-384.57-515.8)">
  <g color-rendering="auto" color-interpolation-filters="linearRGB" shape-rendering="auto" image-rendering="auto" text-rendering="auto" color-interpolation="sRGB" color="#000000">
   <use fill="#f9d24c" xlink:href="#c"/>
   <g transform="scale(1-1)">
    <rect opacity=".5" x="389.57" y="-518.8" width="15" fill="#ffffff" height="1"/>
    <rect opacity=".25" x="389.57" y="-545.8" width="22" height="1"/>
   </g>
   <path opacity=".5" fill="#ffffff" fill-rule="evenodd" d="m411.57 524.8l-7-7v7z"/>
  </g>
  <path opacity=".1" fill="url(#a)" fill-rule="evenodd" d="m404.57 524.8l7 7v-7z"/>
  <path opacity=".75" color-interpolation-filters="linearRGB" color="#000000" image-rendering="auto" color-rendering="auto" d="m14 11c-1.165.411-2 1.507-2 2.814v.381c0 .74-.404 1.378-1 1.723v2.166c.596.345 1 .981 1 1.721v.381c0 1.307.835 2.403 2 2.814v-1.092c-.596-.345-1-.983-1-1.723v-.381c0-1.3-.826-2.388-1.98-2.805 1.154-.417 1.98-1.505 1.98-2.805v-.381c0-.74.404-1.378 1-1.723zm4 0v1.092c.596.345 1 .983 1 1.723v.381c0 1.299.827 2.388 1.98 2.805-1.154.416-1.98 1.505-1.98 2.805v.381c0 .74-.404 1.378-1 1.723v1.092c1.165-.411 2-1.507 2-2.814v-.381c0-.74.404-1.376 1-1.721v-2.166c-.596-.345-1-.983-1-1.723v-.381c0-1.307-.835-2.403-2-2.814m-8.508 10.58v1.42h1.508v-1.42z" color-interpolation="sRGB" text-rendering="auto" fill="#ffffff" shape-rendering="auto" transform="translate(384.57 515.8)"/>
  <use fill="url(#b)" xlink:href="#c"/>
 </g>
</svg>
