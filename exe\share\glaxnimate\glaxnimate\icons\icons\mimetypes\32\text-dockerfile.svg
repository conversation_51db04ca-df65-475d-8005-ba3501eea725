<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:xlink="http://www.w3.org/1999/xlink"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   width="32"
   height="32"
   id="svg2"
   version="1.1"
   inkscape:version="0.92.1 r"
   sodipodi:docname="text-dockerfile.svg">
  <metadata
     id="metadata49">
    <rdf:RDF>
      <cc:Work
         rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
      </cc:Work>
    </rdf:RDF>
  </metadata>
  <sodipodi:namedview
     pagecolor="#ffffff"
     bordercolor="#666666"
     borderopacity="1"
     objecttolerance="10"
     gridtolerance="10"
     guidetolerance="10"
     inkscape:pageopacity="0"
     inkscape:pageshadow="2"
     inkscape:window-width="1920"
     inkscape:window-height="1025"
     id="namedview47"
     showgrid="false"
     inkscape:zoom="4"
     inkscape:cx="19.576677"
     inkscape:cy="15.583349"
     inkscape:window-x="0"
     inkscape:window-y="26"
     inkscape:window-maximized="1"
     inkscape:current-layer="svg2">
    <inkscape:grid
       type="xygrid"
       id="grid4509" />
  </sodipodi:namedview>
  <defs
     id="defs4">
    <linearGradient
       id="a"
       y1="61"
       y2="3"
       x2="0"
       gradientUnits="userSpaceOnUse"
       gradientTransform="matrix(0.5,0,0,-0.48276,0,31.45)">
      <stop
         stop-color="#22a7f0"
         id="stop7" />
      <stop
         offset="1"
         stop-color="#19b5fe"
         id="stop9" />
    </linearGradient>
    <linearGradient
       id="b"
       y1="518.04"
       y2="524.8"
       x2="0"
       gradientUnits="userSpaceOnUse"
       gradientTransform="translate(-384.57,-515.8)">
      <stop
         stop-color="#c8e3fe"
         id="stop12" />
      <stop
         offset="1"
         stop-color="#ffffff"
         id="stop14" />
    </linearGradient>
    <linearGradient
       id="c"
       y1="524.8"
       x1="404.57"
       y2="531.8"
       x2="411.57"
       gradientUnits="userSpaceOnUse"
       gradientTransform="translate(-384.57,-515.8)">
      <stop
         stop-color="#383e51"
         id="stop17" />
      <stop
         offset="1"
         stop-color="#655c6f"
         stop-opacity="0"
         id="stop19" />
    </linearGradient>
    <linearGradient
       inkscape:collect="always"
       id="linearGradient6251">
      <stop
         style="stop-color:#ffffff;stop-opacity:0"
         offset="0"
         id="stop6253" />
      <stop
         style="stop-color:#ffffff;stop-opacity:0.2"
         offset="1"
         id="stop6255" />
    </linearGradient>
    <linearGradient
       inkscape:collect="always"
       xlink:href="#linearGradient6251"
       id="linearGradient4232"
       x1="15.033898"
       y1="30.101694"
       x2="14.932203"
       y2="2.0338984"
       gradientUnits="userSpaceOnUse" />
  </defs>
  <path
     inkscape:connector-curvature="0"
     style="fill:#47cffb;fill-opacity:1"
     id="path23"
     d="m 5,30 0,-28 15,0 7,7 0,21 -7,0 z" />
  <rect
     width="15"
     x="5.0000072"
     y="-2.9999878"
     height="1"
     id="rect27"
     style="fill:#ffffff;fill-opacity:0.41199999"
     transform="scale(1,-1)" />
  <rect
     width="22"
     x="5.0000072"
     y="-29.999989"
     height="1"
     id="rect29"
     style="fill:#2e3132;fill-opacity:0.294"
     transform="scale(1,-1)" />
  <path
     d="M 27,9 20,2 20,9 Z"
     id="path33"
     style="fill:#000000;fill-rule:evenodd;fill-opacity:1;opacity:0.4"
     inkscape:connector-curvature="0" />
  <path
     d="M 27,16 20,9 27,9 Z"
     id="path35"
     style="opacity:0.2;fill:url(#c);fill-rule:evenodd"
     inkscape:connector-curvature="0" />
  <path
     inkscape:connector-curvature="0"
     style="fill:url(#linearGradient4232);fill-opacity:1"
     id="path23-3"
     d="m 5,30 0,-28 15,0 7,7 0,21 -7,0 z" />
  <g
     inkscape:label="Livello 2"
     id="layer2"
     transform="matrix(3.7795276,0,0,3.7795276,8,8.4453125)"
     style="opacity:0.4;vector-effect:none;fill:#000000;fill-opacity:1;stroke-width:0.13229166;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1">
    <path
       id="rect40"
       transform="scale(0.26458333)"
       d="M 6.0097656,3.9882812 V 5.546875 H 7.5683594 V 3.9882812 Z m 2,0 V 5.546875 H 9.5683594 V 3.9882812 Z M 12.269531,5.2714844 C 11.745595,5.6305661 11.504625,6.2807178 11.667969,6.8945312 11.833126,7.5078877 12.366712,7.950717 13,8 c 0.211634,0.5985921 0.777207,0.999109 1.412109,1 0.634902,-8.906e-4 1.200476,-0.4014075 1.41211,-1 -0.211634,-0.5985925 -0.777208,-0.9991094 -1.41211,-1 -0.30289,9.283e-4 -0.598403,0.093532 -0.847656,0.265625 0.101286,-0.2858588 0.113539,-0.5957052 0.03516,-0.8886719 C 13.43469,5.7642304 12.90208,5.321559 12.269531,5.2714844 Z M 13,8 H 1 c -4.2e-7,3.313709 2.6862912,5 6,5 3.313709,0 6,-1.686291 6,-5 z M 3.4414062,5.9960938 V 7.5546875 H 5 V 5.9960938 Z m 2,0 V 7.5546875 H 7 V 5.9960938 Z m 2,0 V 7.5546875 H 9 V 5.9960938 Z m 2,0 V 7.5546875 H 11 V 5.9960938 Z M 3.5,9 A 0.49999997,0.49999997 0 0 1 4,9.5 0.49999997,0.49999997 0 0 1 3.5,10 0.49999997,0.49999997 0 0 1 3,9.5 0.49999997,0.49999997 0 0 1 3.5,9 Z"
       style="color:#000000;clip-rule:nonzero;display:inline;overflow:visible;visibility:visible;opacity:1;isolation:auto;mix-blend-mode:normal;color-interpolation:sRGB;color-interpolation-filters:linearRGB;solid-color:#000000;solid-opacity:1;vector-effect:none;fill:#000000;fill-opacity:1;fill-rule:nonzero;stroke:none;stroke-width:0.5;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;marker:none;color-rendering:auto;image-rendering:auto;shape-rendering:auto;text-rendering:auto;enable-background:accumulate"
       inkscape:connector-curvature="0" />
  </g>
</svg>
